import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:toii_social/cubit/post/home/<USER>';
import 'package:toii_social/screen/home/<USER>/home_item_post_widget.dart';
import 'package:toii_social/screen/home/<USER>/home_skeleton_widget.dart';
import 'package:toii_social/widget/colors/colors.dart';
import 'package:toii_social/widget/colors/text_style.dart';

class HomeFollowingWidget extends StatelessWidget {
  const HomeFollowingWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<HomeCubit, HomeState>(
      builder: (context, state) {
        // Show loading skeleton when loading and no posts
        if (state.followingStatus.isLoading && state.followingPosts.isEmpty) {
          return const HomeSkeletonListWidget(itemCount: 5);
        }

        // Show error state
        if (state.followingStatus.isFailure && state.followingPosts.isEmpty) {
          return SliverToBoxAdapter(
            child: Container(
              padding: const EdgeInsets.all(16),
              child: Column(
                children: [
                  Container(
                    height: 200,
                    decoration: BoxDecoration(
                      color: context.themeData.neutral100,
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: context.themeData.neutral200,
                        width: 1,
                      ),
                    ),
                    child: Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.error_outline,
                            size: 48,
                            color: context.themeData.neutral400,
                          ),
                          const SizedBox(height: 12),
                          Text(
                            'Failed to load following feed',
                            style: titleMedium.copyWith(
                              color: context.themeData.neutral500,
                            ),
                          ),
                          const SizedBox(height: 8),
                          Text(
                            state.followingErrorMessage ??
                                'Something went wrong',
                            style: bodyMedium.copyWith(
                              color: context.themeData.neutral400,
                            ),
                            textAlign: TextAlign.center,
                          ),
                          const SizedBox(height: 16),
                          ElevatedButton(
                            onPressed: () {
                              context.read<HomeCubit>().getUserFeedFollowing(
                                isRefresh: true,
                              );
                            },
                            child: const Text('Retry'),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          );
        }

        // Show empty state when no posts
        if (state.followingPosts.isEmpty && state.followingStatus.isSuccess) {
          return SliverToBoxAdapter(
            child: Container(
              padding: const EdgeInsets.all(16),
              child: Column(
                children: [
                  Container(
                    height: 200,
                    decoration: BoxDecoration(
                      color: context.themeData.neutral100,
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: context.themeData.neutral200,
                        width: 1,
                      ),
                    ),
                    child: Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.people_outline,
                            size: 48,
                            color: context.themeData.neutral400,
                          ),
                          const SizedBox(height: 12),
                          Text(
                            'No posts from following',
                            style: titleMedium.copyWith(
                              color: context.themeData.neutral500,
                            ),
                          ),
                          const SizedBox(height: 8),
                          Text(
                            'Posts from people you follow will appear here',
                            style: bodyMedium.copyWith(
                              color: context.themeData.neutral400,
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          );
        }

        // Show posts list with loading indicator
        return Column(
          children: [
            SliverList(
              delegate: SliverChildBuilderDelegate((context, index) {
                if (index < state.followingPosts.length) {
                  return HomeItemPostWidget(
                    post: state.followingPosts[index],
                    isShowActionMore: true,
                    isNotOnTap: false,
                  );
                } else {
                  return const SizedBox.shrink();
                }
              }, childCount: state.followingPosts.length),
            ),
            // SliverToBoxAdapter(
            //   child:
            //       state.canLoadMoreFollowing
            //           ? Container(
            //             height: 64,
            //             margin: const EdgeInsets.only(bottom: 16),
            //             alignment: Alignment.center,
            //             child: const Center(child: CircularProgressIndicator()),
            //           )
            //           : const SizedBox(),
            // ),
          ],
        );
      },
    );
  }
}
