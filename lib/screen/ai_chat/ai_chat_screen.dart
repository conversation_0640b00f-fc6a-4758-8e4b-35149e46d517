import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_chat_types/flutter_chat_types.dart' as types;
import 'package:flutter_chat_ui/flutter_chat_ui.dart';
import 'package:toii_social/cubit/ai_chat/ai_chat_cubit.dart';
import 'package:toii_social/locator/locator.dart';
import 'package:toii_social/widget/app_bar/app_bar.dart';
import 'package:toii_social/widget/colors/colors.dart';
import 'package:toii_social/widget/colors/text_style.dart';

class AiChatScreen extends StatelessWidget {
  const AiChatScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => AiChatCubit(aiChatRepository: serviceLocator()),
      child: const _AiChatView(),
    );
  }
}

class _AiChatView extends StatelessWidget {
  const _AiChatView();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: context.themeData.neutral50,
      appBar: BaseAppBar(
        widgetTitle: Row(
          children: [
            Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                color: context.themeData.primaryGreen500,
                shape: BoxShape.circle,
              ),
              child: const Icon(Icons.smart_toy, color: Colors.white, size: 24),
            ),
            const SizedBox(width: 12),
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Gao',
                  style: titleMedium.copyWith(
                    color: context.themeData.textPrimary,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                Text(
                  'AI Assistant',
                  style: bodySmall.copyWith(
                    color: context.themeData.textSecondary,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
      body: GestureDetector(
        onTap: () {
          // Hide keyboard when tapping outside
          FocusScope.of(context).unfocus();
        },
        child: BlocBuilder<AiChatCubit, AiChatState>(
          builder: (context, state) {
            return Center(
              child: Column(
                children: [
                  // Chat messages area
                  Expanded(
                    child: Stack(
                      children: [
                        Chat(
                          messages: state.messages,
                          onSendPressed:
                              (message) => _handleSendPressed(context, message),
                          user: context.read<AiChatCubit>().user,
                          theme: _buildChatTheme(context),
                          showUserAvatars: true,
                          showUserNames: false,
                          onMessageLongPress: (context, message) => _handleMessageLongPress(context, message),
                          bubbleBuilder: (child,  message, nextMessageInGroup) => _buildCustomBubble(
                            context,
                            child,
                            message,
                            nextMessageInGroup,
                          ),
                          inputOptions: InputOptions(
                            sendButtonVisibilityMode:
                                SendButtonVisibilityMode.always,
                            enabled: state.status != AiChatStatus.streaming,
                          ),
                          l10n: const ChatL10nEn(
                            inputPlaceholder: 'Ask Gao anything...',
                          ),
                          emptyState: _buildEmptyState(context),
                        ),
                        // Streaming indicator
                        if (state.status == AiChatStatus.streaming)
                          _buildStreamingIndicator(context),
                      ],
                    ),
                  ),
                ],
              ),
            );
          },
        ),
      ),
    );
  }

  Widget _buildEmptyState(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(24),
      child: ConstrainedBox(
        constraints: BoxConstraints(
          minHeight:
              MediaQuery.of(context).size.height -
              MediaQuery.of(context).padding.top -
              kToolbarHeight -
              MediaQuery.of(context).viewInsets.bottom -
              48,
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              width: 80,
              height: 80,
              decoration: BoxDecoration(
                color: context.themeData.primaryGreen100,
                shape: BoxShape.circle,
              ),
              child: Icon(
                Icons.smart_toy,
                size: 40,
                color: context.themeData.primaryGreen500,
              ),
            ),
            const SizedBox(height: 24),
            Text(
              'Welcome to Gao!',
              style: headlineSmall.copyWith(
                color: context.themeData.textPrimary,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Your AI assistant is ready to help.\nAsk me anything!',
              textAlign: TextAlign.center,
              style: bodyMedium.copyWith(
                color: context.themeData.textSecondary,
              ),
            ),
            const SizedBox(height: 32),
            _buildSuggestedQuestions(context),
          ],
        ),
      ),
    );
  }

  Widget _buildSuggestedQuestions(BuildContext context) {
    final suggestions = [
      'What can you help me with?',
      'Tell me about GaoSocial',
      'How do I use this app?',
    ];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Try asking:',
          style: labelMedium.copyWith(color: context.themeData.textSecondary),
        ),
        const SizedBox(height: 12),
        ...suggestions.map(
          (suggestion) => Padding(
            padding: const EdgeInsets.only(bottom: 8),
            child: InkWell(
              onTap:
                  () => _handleSendPressed(
                    context,
                    types.PartialText(text: suggestion),
                  ),
              borderRadius: BorderRadius.circular(12),
              child: Container(
                width: double.infinity,
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  border: Border.all(color: context.themeData.neutral200),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  suggestion,
                  style: bodyMedium.copyWith(
                    color: context.themeData.textPrimary,
                  ),
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildStreamingIndicator(BuildContext context) {
    return Positioned(
      top: 16,
      left: 16,
      right: 16,
      child: Center(
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          decoration: BoxDecoration(
            color: context.themeData.primaryGreen500.withValues(alpha: 0.9),
            borderRadius: BorderRadius.circular(20),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.1),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              SizedBox(
                width: 16,
                height: 16,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: const AlwaysStoppedAnimation<Color>(Colors.white),
                ),
              ),
              const SizedBox(width: 8),
              Text(
                'Gao is typing...',
                style: bodySmall.copyWith(color: Colors.white),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _handleSendPressed(BuildContext context, types.PartialText message) {
    context.read<AiChatCubit>().sendMessage(message.text);
  }

  void _handleMessageLongPress(BuildContext context, types.Message message) {
    String text = '';
    if (message is types.TextMessage) {
      text = message.text;
    }
    
    Clipboard.setData(ClipboardData(text: text));
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: const Text('Message copied to clipboard'),
        backgroundColor: context.themeData.primaryGreen500,
        behavior: SnackBarBehavior.floating,
        duration: const Duration(seconds: 2),
      ),
    );
  }

  void _copyMessageText(BuildContext context, String text) {
    Clipboard.setData(ClipboardData(text: text));
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: const Text('Message copied to clipboard'),
        backgroundColor: context.themeData.primaryGreen500,
        behavior: SnackBarBehavior.floating,
        duration: const Duration(seconds: 2),
      ),
    );
  }

  Widget _buildCustomBubble(
    BuildContext context,
    Widget child,
    types.Message message,
    bool nextMessageInGroup,
  ) {
    final isUser = message.author.id == context.read<AiChatCubit>().user.id;
    
    return Column(
      crossAxisAlignment: isUser ? CrossAxisAlignment.end : CrossAxisAlignment.start,
      children: [
        child,
        if (message is types.TextMessage)
          Padding(
            padding: EdgeInsets.only(
              top: 4,
              left: isUser ? 60 : 0,
              right: isUser ? 0 : 60,
            ),
            child: GestureDetector(
              onTap: () => _copyMessageText(context, message.text),
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: context.themeData.neutral100,
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(color: context.themeData.neutral200),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      Icons.copy,
                      size: 14,
                      color: context.themeData.textSecondary,
                    ),
                    const SizedBox(width: 4),
                    Text(
                      'Copy',
                      style: bodySmall.copyWith(
                        color: context.themeData.textSecondary,
                        fontSize: 12,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
      ],
    );
  }

  ChatTheme _buildChatTheme(BuildContext context) {
    final themeData = context.themeData;

    return DefaultChatTheme(
      backgroundColor: themeData.neutral50,
      primaryColor: themeData.primaryGreen500,
      secondaryColor: themeData.neutral100,
      inputBackgroundColor: Colors.white,
      inputTextColor: themeData.textPrimary,
      messageBorderRadius: 16,
      userAvatarNameColors: [themeData.primaryGreen500],
      inputBorderRadius: const BorderRadius.all(Radius.circular(24)),
      inputPadding: const EdgeInsets.symmetric(horizontal: 20, vertical: 14),
      inputMargin: const EdgeInsets.fromLTRB(16, 8, 16, 24),
      sentMessageBodyTextStyle: bodyMedium.copyWith(
        color: Colors.white,
        height: 1.4,
      ),
      receivedMessageBodyTextStyle: bodyMedium.copyWith(
        color: themeData.textPrimary,
        height: 1.4,
      ),
      sentMessageCaptionTextStyle: bodySmall.copyWith(
        color: Colors.white.withValues(alpha: 0.7),
      ),
      receivedMessageCaptionTextStyle: bodySmall.copyWith(
        color: themeData.textSecondary,
      ),
      // Enhanced message styling
      sentMessageDocumentIconColor: Colors.white,
      receivedMessageDocumentIconColor: themeData.textSecondary,
      inputTextStyle: bodyMedium.copyWith(color: themeData.textPrimary),
      // Enhanced send button styling
      sendButtonIcon: Container(
        width: 36,
        height: 36,
        decoration: BoxDecoration(
          color: themeData.primaryGreen500,
          shape: BoxShape.circle,
          boxShadow: [
            BoxShadow(
              color: themeData.primaryGreen500.withValues(alpha: 0.3),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Icon(Icons.send_rounded, color: themeData.neutral50, size: 18),
      ),
      sendButtonMargin: const EdgeInsets.only(left: 8, right: 4),
      // Message margins
      messageInsetsHorizontal: 16,
      messageInsetsVertical: 8,
      // Avatar styling
      userAvatarImageBackgroundColor: themeData.primaryGreen500,
      // Enhanced input decoration
      inputContainerDecoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(24),
        border: Border.all(color: themeData.neutral200, width: 1),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.08),
            blurRadius: 12,
            offset: const Offset(0, 2),
          ),
        ],
      ),
    );
  }
}
