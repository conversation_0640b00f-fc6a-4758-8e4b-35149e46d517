import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:get_it/get_it.dart';
import 'package:go_router/go_router.dart';
import 'package:toii_social/cubit/auth/profile/profile_cubit.dart';
import 'package:toii_social/cubit/user/avatar_upload/avatar_upload_cubit.dart';
import 'package:toii_social/cubit/user/edit_profile/edit_profile_cubit.dart';
import 'package:toii_social/gen/assets.gen.dart';
import 'package:toii_social/model/user/user_model.dart';
import 'package:toii_social/screen/user_profile/edit_profile/widgets/avatar_section_widget.dart';
import 'package:toii_social/screen/user_profile/edit_profile/widgets/avatar_selection_bottom_sheet.dart';
import 'package:toii_social/screen/user_profile/edit_profile/widgets/profile_form_fields_widget.dart';
import 'package:toii_social/widget/colors/colors.dart';
import 'package:toii_social/widget/colors/text_style.dart';
import 'package:toii_social/widget/snack_bar/snackbar.dart';

class EditProfileScreen extends StatefulWidget {
  final UserModel? user;

  const EditProfileScreen({super.key, this.user});

  @override
  State<EditProfileScreen> createState() => _EditProfileScreenState();
}

class _EditProfileScreenState extends State<EditProfileScreen> {
  late EditProfileCubit _editProfileCubit;
  late AvatarUploadCubit _avatarUploadCubit;
  late TextEditingController _displayNameController;
  late TextEditingController _bioController;

  @override
  void initState() {
    super.initState();
    _editProfileCubit = EditProfileCubit();
    _avatarUploadCubit = GetIt.instance<AvatarUploadCubit>();
    _displayNameController = TextEditingController();
    _bioController = TextEditingController();

    if (widget.user != null) {
      _editProfileCubit.initializeProfile(widget.user!);
      _displayNameController.text =
          widget.user!.fullName ?? widget.user!.firstName ?? '';
      _bioController.text = widget.user!.bio ?? '';
    }

    // Add listener to update character count
    _bioController.addListener(() {
      setState(() {});
    });
  }

  @override
  void dispose() {
    _displayNameController.dispose();
    _bioController.dispose();
    _editProfileCubit.resetState();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider.value(value: _editProfileCubit),
        BlocProvider.value(value: _avatarUploadCubit),
      ],
      child: MultiBlocListener(
        listeners: [
          BlocListener<EditProfileCubit, EditProfileState>(
            listener: (context, state) {
              if (state.status.isSuccess) {
                context.showSnackbar(message: "Edit profile successfully");
                GetIt.instance<ProfileCubit>().getProfile();

                context.pop();
              } else if (state.status.isFailure) {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text(
                      state.errorMessage ?? 'Failed to update profile',
                    ),
                  ),
                );
              }
            },
          ),
          BlocListener<AvatarUploadCubit, AvatarUploadState>(
            listener: (context, state) {
              if (state.status.isLoading) {
                EasyLoading.show();
              }
              if (state.status.isSuccess && state.localImagePath != null) {
                EasyLoading.dismiss();
                context.pop();
                // Update the edit profile cubit with the selected image path
                _editProfileCubit.updateAvatarWithKey(
                  widget.user!.id,
                  _avatarUploadCubit.state.mediaKey!,
                );

                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('Avatar updated successfully')),
                );
                GetIt.instance<ProfileCubit>().getProfile();
              } else if (state.status.isFailure) {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text(
                      state.errorMessage ?? 'Failed to select avatar',
                    ),
                  ),
                );
              }
            },
          ),
        ],
        child: BlocBuilder<EditProfileCubit, EditProfileState>(
          builder: (context, state) {
            return Scaffold(
              backgroundColor: const Color(
                0xFF0F0F0F,
              ), // Keep as hardcoded for dark background
              resizeToAvoidBottomInset: true, // Handle keyboard properly
              body: Stack(
                children: [
                  // Background image
                  Positioned.fill(
                    child: Assets.images.defaultBackground.image(
                      fit: BoxFit.cover,
                    ),
                  ),
                  // Gradient overlays
                  Positioned.fill(
                    child: Container(
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          begin: Alignment.topCenter,
                          end: Alignment.bottomCenter,
                          colors: [
                            Colors.transparent,
                            const Color(0xFF0F0F0F).withValues(alpha: 0.8),
                          ],
                        ),
                      ),
                    ),
                  ),
                  // Main content
                  GestureDetector(
                    onTap: () {
                      // Hide keyboard when tapping outside
                      FocusScope.of(context).unfocus();
                    },
                    child: SafeArea(
                      child: Column(
                        children: [
                          _buildAppBar(context, state),
                          Expanded(
                            child: SingleChildScrollView(
                              padding: const EdgeInsets.only(bottom: 20),
                              child: ConstrainedBox(
                                constraints: BoxConstraints(
                                  minHeight:
                                      MediaQuery.of(context).size.height -
                                      MediaQuery.of(context).padding.top -
                                      MediaQuery.of(context).padding.bottom -
                                      100, // Subtract app bar height
                                ),
                                child: IntrinsicHeight(
                                  child: Column(
                                    children: [
                                      const Spacer(), // This will push content to bottom
                                      Stack(
                                        clipBehavior: Clip.none,
                                        children: [
                                          Column(
                                            children: [
                                              const SizedBox(
                                                height: 72,
                                              ), // Space for avatar
                                              _buildFormCard(state),
                                            ],
                                          ),
                                          // Avatar positioned to overlap the card
                                          Positioned(
                                            top: 36,
                                            left: 0,
                                            right: 0,
                                            child: AvatarSectionWidget(
                                              state: state,
                                              user: widget.user,
                                              onAvatarTap: _selectAvatar,
                                            ),
                                          ),
                                        ],
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            );
          },
        ),
      ),
    );
  }

  Widget _buildAppBar(BuildContext context, EditProfileState state) {
    final themeData = Theme.of(context);
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      child: Row(
        children: [
          // Close button
          Container(
            decoration: BoxDecoration(
              color: themeData.white200,
              borderRadius: BorderRadius.circular(12),
            ),
            child: Material(
              color: Colors.transparent,
              child: InkWell(
                borderRadius: BorderRadius.circular(12),
                onTap: () => context.pop(),
                child: Container(
                  width: 32,
                  height: 32,
                  padding: const EdgeInsets.all(6),
                  child: Icon(
                    Icons.close,
                    color: themeData.textContrast,
                    size: 20,
                  ),
                ),
              ),
            ),
          ),
          // Title
          const SizedBox(width: 16),
          Text(
            'Edit profile',
            style: titleLarge.copyWith(
              color: themeData.textContrast,
              fontWeight: FontWeight.w600,
            ),
            textAlign: TextAlign.center,
          ),
          const Spacer(),
          // Save button
          Container(
            decoration: BoxDecoration(
              color:
                  state.hasChanges && !state.status.isLoading
                      ? themeData.primaryGreen500
                      : themeData.neutral100,
              borderRadius: BorderRadius.circular(24),
            ),
            child: Material(
              color: Colors.transparent,
              child: InkWell(
                borderRadius: BorderRadius.circular(24),
                onTap:
                    state.hasChanges && !state.status.isLoading
                        ? () => _saveProfile()
                        : null,
                child: Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 8,
                  ),
                  child:
                      state.status.isLoading
                          ? SizedBox(
                            width: 16,
                            height: 16,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              color: themeData.textContrast,
                            ),
                          )
                          : Text(
                            'Save',
                            style: titleMedium.copyWith(
                              color:
                                  state.hasChanges && !state.status.isLoading
                                      ? themeData.textContrast
                                      : themeData.neutral300,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFormCard(EditProfileState state) {
    final themeData = Theme.of(context);
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 8),
      padding: const EdgeInsets.only(top: 60, bottom: 20, left: 16, right: 16),
      decoration: BoxDecoration(
        color: themeData.white900,
        borderRadius: BorderRadius.circular(28),
        boxShadow: [
          BoxShadow(
            color: themeData.black50,
            blurRadius: 16,
            offset: const Offset(0, 8),
          ),
        ],
      ),
      child: ProfileFormFieldsWidget(
        state: state,
        user: widget.user,
        displayNameController: _displayNameController,
        bioController: _bioController,
        onDisplayNameChanged:
            (value) => _editProfileCubit.updateDisplayName(value),
        onBioChanged: (value) => _editProfileCubit.updateBio(value),
      ),
    );
  }

  void _selectAvatar() {
    AvatarSelectionBottomSheet.show(
      context: context,
      onGalleryTap: () => _avatarUploadCubit.pickImageFromGallery(),
      onCameraTap: () => _avatarUploadCubit.pickImageFromCamera(),
      onAIGenerateTap: () {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(const SnackBar(content: Text('Coming soon')));
      },
    );
  }

  void _saveProfile() {
    if (widget.user?.id != null) {
      _editProfileCubit.saveProfile(widget.user!.id);
    }
  }
}
