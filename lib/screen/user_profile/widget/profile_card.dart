import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:readmore/readmore.dart';
import 'package:toii_social/cubit/user/user_stats/user_stats_cubit.dart';
import 'package:toii_social/gen/assets.gen.dart';
import 'package:toii_social/model/user/user_model.dart';
import 'package:toii_social/router/app_router.dart';
import 'package:toii_social/screen/user_profile/widget/stat_widget.dart';
import 'package:toii_social/widget/button/button.dart';
import 'package:toii_social/widget/colors/colors.dart';
import 'package:toii_social/widget/colors/text_style.dart';

class ProfileCard extends StatefulWidget {
  const ProfileCard({super.key, required this.user, required this.isMyProfile});
  final UserModel? user;
  final bool isMyProfile;

  @override
  State<ProfileCard> createState() => _ProfileCardState();
}

class _ProfileCardState extends State<ProfileCard> {
  String _getDisplayCount(int? count, UserStatsStatus status) {
    if (status.isLoading) return "...";
    if (status.isFailure || count == null) return "-";
    return count.toString();
  }

  @override
  Widget build(BuildContext context) {
    final themeData = context.themeData;
    return BlocBuilder<UserStatsCubit, UserStatsState>(
      builder: (context, state) {
        return Stack(
          clipBehavior: Clip.none,
          alignment: Alignment.topCenter,
          children: [
            Container(
              margin: const EdgeInsets.only(
                top: 48,
                left: 20,
                right: 20,
                bottom: 0,
              ),
              padding: const EdgeInsets.only(top: 60, bottom: 20),
              decoration: BoxDecoration(
                color: Colors.white.withOpacity(0.95),
                borderRadius: BorderRadius.circular(30),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.06),
                    blurRadius: 16,
                    offset: const Offset(0, 8),
                  ),
                ],
              ),
              child: Column(
                children: [
                  Text(
                    (state.userStats?.fullName?.isNotEmpty == true)
                        ? state.userStats?.fullName ?? ""
                        : (state.userStats?.username ?? '-'),
                    style: titleLarge.copyWith(color: themeData.neutral800),
                  ),
                  if (widget.user?.bio != null &&
                      (state.userStats?.bio?.isNotEmpty ?? false))
                    Padding(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 24.0,
                        vertical: 8,
                      ),
                      child: ReadMoreText(
                        state.userStats!.bio!,
                        trimLines: 2,
                        trimMode: TrimMode.Line,
                        trimCollapsedText: ' See more',
                        trimExpandedText: '',
                        style: labelLarge.copyWith(color: themeData.neutral400),
                        moreStyle: labelLarge.copyWith(
                          color: themeData.primaryGreen500,
                        ),
                      ),
                    ),
                  const SizedBox(height: 12),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                    children: [
                      StatWidget(
                        label: "Follower",
                        count: _getDisplayCount(
                          state.userStats?.followersCount,
                          state.status,
                        ),
                        onTap: () {
                          context.push(
                            RouterEnums.followerFollowing.routeName,
                            extra: state.userStats,
                          );
                        },
                      ),
                      StatWidget(
                        label: "Following",
                        count: _getDisplayCount(
                          state.userStats?.followingCount,
                          state.status,
                        ),
                        onTap: () {
                          context.push(
                            RouterEnums.followerFollowing.routeName,
                            extra: state.userStats,
                          );
                        },
                      ),
                      StatWidget(
                        label: "Posts",
                        count: _getDisplayCount(
                          state.userStats?.postsCount,
                          state.status,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 20),
                  if (!widget.isMyProfile)
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 16),
                      child: Row(
                        children: [
                          Expanded(
                            child: TSButton.primary(
                              size: ButtonSize.small,
                              title: "Follow",
                              onPressed: () {},
                            ),
                          ),
                          const SizedBox(width: 12),
                          Assets.icons.icIbox.svg(),
                        ],
                      ),
                    ),
                ],
              ),
            ),
            if (widget.isMyProfile)
              Positioned(
                top: 56,
                right: 32,
                child: InkWell(
                  onTap: () {
                    context
                        .push(
                          RouterEnums.editProfile.routeName,
                          extra: state.userStats,
                        )
                        .then((value) {
                          context.read<UserStatsCubit>().getUserStats(
                            widget.user?.id ?? "",
                          );
                        });
                  },
                  borderRadius: BorderRadius.circular(20),
                  child: Container(
                    padding: const EdgeInsets.all(8),
                    child: Assets.icons.icEditProfile.svg(),
                  ),
                ),
              ),
            Positioned(
              top: 0,
              child: Container(
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.white, width: 4),
                  shape: BoxShape.circle,
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.08),
                      blurRadius: 8,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: CircleAvatar(
                  radius: 48,
                  backgroundImage: NetworkImage(
                    state.userStats?.avatarUrl ?? '',
                  ),
                ),
              ),
            ),
          ],
        );
      },
    );
  }
}
