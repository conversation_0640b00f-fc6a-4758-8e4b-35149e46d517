import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:dio/dio.dart';
import 'package:retrofit/retrofit.dart';
import 'package:toii_social/model/streaming/streaming_exception.dart';
import 'package:toii_social/model/streaming/streaming_response_model.dart';

part 'streaming_service.g.dart';

@RestApi()
abstract class StreamingService {
  factory StreamingService(Dio dio, {String baseUrl}) = _StreamingService;

  /// Generic streaming endpoint for Server-Sent Events
  @GET('{path}')
  Future<ResponseBody> getStreamingResponse(
    @Path('path') String path, {
    @Queries() Map<String, dynamic>? queryParameters,
    @Header('Accept') String accept = 'text/event-stream',
    @Header('Cache-Control') String cacheControl = 'no-cache',
  });

  /// POST request with streaming response
  @POST('{path}')
  Future<ResponseBody> postStreamingResponse(
    @Path('path') String path,
    @Body() Map<String, dynamic> body, {
    @Queries() Map<String, dynamic>? queryParameters,
    @Header('Accept') String accept = 'text/event-stream',
    @Header('Cache-Control') String cacheControl = 'no-cache',
    @Header('Content-Type') String contentType = 'application/json',
  });
}

/// Streaming service implementation with additional helper methods
class StreamingServiceImpl {
  final StreamingService _streamingService;
  final Dio _dio;

  StreamingServiceImpl({
    required StreamingService streamingService,
    required Dio dio,
  })  : _streamingService = streamingService,
        _dio = dio;

  /// Create a stream from Server-Sent Events
  Stream<StreamingResponse<StreamingChunk>> createSSEStream({
    required String path,
    Map<String, dynamic>? queryParameters,
    Map<String, dynamic>? body,
    Map<String, String>? headers,
    Duration? timeout,
    CancelToken? cancelToken,
  }) async* {
    StreamController<StreamingResponse<StreamingChunk>>? controller;
    ResponseBody? responseBody;

    try {
      yield StreamingResponse.connecting();

      // Make the HTTP request
      if (body != null) {
        responseBody = await _streamingService.postStreamingResponse(
          path,
          body,
          queryParameters: queryParameters,
        );
      } else {
        responseBody = await _streamingService.getStreamingResponse(
          path,
          queryParameters: queryParameters,
        );
      }

      // Create stream controller
      controller = StreamController<StreamingResponse<StreamingChunk>>();

      // Process the response stream
      await _processSSEStream(
        responseBody.stream,
        controller,
        timeout: timeout,
        cancelToken: cancelToken,
      );

      yield* controller.stream;
    } catch (e) {
      if (e is DioException) {
        yield StreamingResponse.error(_handleDioError(e));
      } else {
        yield StreamingResponse.error('Unexpected error: ${e.toString()}');
      }
    } finally {
      await controller?.close();
      responseBody?.stream.listen(null).cancel();
    }
  }

  /// Process Server-Sent Events stream
  Future<void> _processSSEStream(
    Stream<List<int>> stream,
    StreamController<StreamingResponse<StreamingChunk>> controller,
    {
    Duration? timeout,
    CancelToken? cancelToken,
  }) async {
    final buffer = StringBuffer();
    StreamSubscription<List<int>>? subscription;

    try {
      subscription = stream.listen(
        (List<int> data) {
          try {
            // Check if cancelled
            if (cancelToken?.isCancelled == true) {
              controller.add(StreamingResponse.cancelled());
              return;
            }

            final chunk = utf8.decode(data);
            buffer.write(chunk);

            // Process complete SSE messages
            _processSSEBuffer(buffer, controller);
          } catch (e) {
            controller.add(StreamingResponse.error(
              'Error processing stream chunk: ${e.toString()}',
            ));
          }
        },
        onError: (error) {
          controller.add(StreamingResponse.error(
            'Stream error: ${error.toString()}',
          ));
        },
        onDone: () {
          // Process any remaining data in buffer
          if (buffer.isNotEmpty) {
            _processSSEBuffer(buffer, controller, isComplete: true);
          }
          controller.add(StreamingResponse.completed(null));
        },
      );

      // Apply timeout if specified
      if (timeout != null) {
        Timer(timeout, () {
          if (!controller.isClosed) {
            subscription?.cancel();
            controller.add(StreamingResponse.error(
              'Stream timeout after ${timeout.inSeconds} seconds',
            ));
          }
        });
      }

      // Wait for stream completion
      await controller.stream.last;
    } catch (e) {
      controller.add(StreamingResponse.error(
        'Error in stream processing: ${e.toString()}',
      ));
    } finally {
      await subscription?.cancel();
    }
  }

  /// Process SSE buffer and extract complete messages
  void _processSSEBuffer(
    StringBuffer buffer,
    StreamController<StreamingResponse<StreamingChunk>> controller, {
    bool isComplete = false,
  }) {
    final content = buffer.toString();
    final messages = content.split('\n\n');

    // Process all complete messages except the last one (unless isComplete)
    final messagesToProcess = isComplete ? messages : messages.sublist(0, messages.length - 1);

    for (final message in messagesToProcess) {
      if (message.trim().isEmpty) continue;

      try {
        final sseData = SSEData.fromRawData(message);

        if (sseData.isEndOfStream) {
          controller.add(StreamingResponse.completed(null));
          return;
        }

        if (!sseData.isEmpty && sseData.data != null) {
          final jsonData = json.decode(sseData.data!);
          final streamingChunk = StreamingChunk.fromJson(jsonData);
          controller.add(StreamingResponse.streaming(streamingChunk));
        }
      } catch (e) {
        controller.add(StreamingResponse.error(
          'Error parsing SSE message: ${e.toString()}',
        ));
      }
    }

    // Update buffer with remaining incomplete message
    if (!isComplete && messages.isNotEmpty) {
      buffer.clear();
      buffer.write(messages.last);
    } else {
      buffer.clear();
    }
  }

  /// Handle Dio errors and convert to meaningful messages
  String _handleDioError(DioException error) {
    switch (error.type) {
      case DioExceptionType.connectionTimeout:
        return 'Connection timeout. Please check your internet connection.';
      case DioExceptionType.sendTimeout:
        return 'Request timeout. Please try again.';
      case DioExceptionType.receiveTimeout:
        return 'Response timeout. The server is taking too long to respond.';
      case DioExceptionType.badResponse:
        final statusCode = error.response?.statusCode;
        if (statusCode == 429) {
          return 'Rate limit exceeded. Please try again later.';
        } else if (statusCode == 401) {
          return 'Unauthorized. Please check your authentication.';
        } else if (statusCode == 403) {
          return 'Forbidden. You do not have permission to access this resource.';
        } else if (statusCode == 404) {
          return 'Resource not found.';
        } else if (statusCode != null && statusCode >= 500) {
          return 'Server error. Please try again later.';
        }
        return 'Request failed with status code: $statusCode';
      case DioExceptionType.cancel:
        return 'Request was cancelled.';
      case DioExceptionType.connectionError:
        return 'Connection error. Please check your internet connection.';
      case DioExceptionType.badCertificate:
        return 'SSL certificate error.';
      case DioExceptionType.unknown:
      default:
        return 'An unexpected error occurred: ${error.message}';
    }
  }
}
