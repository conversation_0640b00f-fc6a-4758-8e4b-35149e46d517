import 'package:toii_social/core/service/ai_chat_service.dart';
import 'package:toii_social/core/service/ai_chat_stream_service.dart';
import 'package:toii_social/model/ai_chat/ai_chat_request_model.dart';
import 'package:toii_social/model/ai_chat/ai_chat_response_model.dart';

abstract class AiChatRepository {
  Future<AiChatResponseModel> sendMessage(String userMessage);
  Future<Stream<String>> sendStreamingMessage(String userMessage);
}

class AiChatRepositoryImpl implements AiChatRepository {
  final AiChatService _aiChatService;
  final AiChatStreamService _aiChatStreamService;
  static const String _bearerToken =
      'Bearer rpa_DQSHSPXPOEVGF9HWMBMUFT59O9ORMERVM2QQE36C1mz6bk';
  static const String _systemMessage =
      'You are Gao, a friendly, creative, and witty AI assistant for GAO social Media.';

  AiChatRepositoryImpl({
    required AiChatService aiChatService,
    required AiChatStreamService aiChatStreamService,
  }) : _aiChatService = aiChatService,
       _aiChatStreamService = aiChatStreamService;

  @override
  Future<AiChatResponseModel> sendMessage(String userMessage) async {
    final request = AiChatRequestModel(
      messages: [
        const AiChatMessageModel(role: 'system', content: _systemMessage),
        AiChatMessageModel(role: 'user', content: userMessage),
      ],
      includeReasoning: false,
      stream: false,
    );

    return await _aiChatService.sendMessage(request, _bearerToken);
  }

  @override
  Future<Stream<String>> sendStreamingMessage(String userMessage) async {
    final request = AiChatRequestModel(
      messages: [
        const AiChatMessageModel(role: 'system', content: _systemMessage),
        AiChatMessageModel(role: 'user', content: userMessage),
      ],
      includeReasoning: true,
      stream: true,
    );

    return await _aiChatStreamService.sendStreamingMessage(
      request,
      _bearerToken,
    );
  }
}
