import 'package:toii_social/core/service/user_service.dart';
import 'package:toii_social/model/base/base_response.dart';
import 'package:toii_social/model/follower/follower_model.dart';
import 'package:toii_social/model/user/update_user_request_model.dart';
import 'package:toii_social/model/user/user_model.dart';

abstract class UserRepository {
  Future<BaseResponse<UserModel>> getUserStats(String userId);

  Future<BaseResponse<FollowingListModel>> getFollowing();

  // New methods for API response with relationship structure
  Future<FollowerApiResponseModel> getFollowersNew();
  Future<FollowerApiResponseModel> getFollowingNew();
  Future<FollowerApiResponseModel> getUserFollowingNew(String userId);
  Future<FollowerApiResponseModel> getUserFollowerNew(String userId);
  Future<BaseResponse<UserModel>> updateUser(
    String userId,
    UpdateUserRequestModel request,
  );
}

class UserRepositoryImpl extends UserRepository {
  final UserService userService;

  UserRepositoryImpl({required this.userService});

  @override
  Future<BaseResponse<UserModel>> getUserStats(String userId) async {
    try {
      return await userService.getUserStats(userId);
    } catch (e) {
      rethrow;
    }
  }

  @override
  Future<BaseResponse<FollowingListModel>> getFollowing() {
    return userService.getFollowing();
  }

  // New methods implementation
  @override
  Future<FollowerApiResponseModel> getFollowersNew() {
    return userService.getFollowersNew();
  }

  @override
  Future<FollowerApiResponseModel> getFollowingNew() {
    return userService.getFollowingNew();
  }

  @override
  Future<FollowerApiResponseModel> getUserFollowingNew(String userId) {
    return userService.getUserFollowingNew(userId);
  }

  @override
  Future<BaseResponse<UserModel>> updateUser(
    String userId,
    UpdateUserRequestModel request,
  ) async {
    try {
      return await userService.updateUser(userId, request);
    } catch (e) {
      rethrow;
    }
  }
  
  @override
  Future<FollowerApiResponseModel> getUserFollowerNew(String userId) {
    return userService.getUserFollowerNew(userId);
  }
}
