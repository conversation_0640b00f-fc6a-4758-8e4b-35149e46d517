import 'dart:async';

import 'package:dio/dio.dart';
import 'package:toii_social/core/service/streaming_service.dart';
import 'package:toii_social/model/streaming/streaming_exception.dart';
import 'package:toii_social/model/streaming/streaming_response_model.dart';

/// Abstract repository for streaming operations
abstract class StreamingRepository {
  /// Create a streaming connection to the specified endpoint
  Stream<StreamingResponse<StreamingChunk>> createStream({
    required String path,
    Map<String, dynamic>? queryParameters,
    Map<String, dynamic>? body,
    Map<String, String>? headers,
    Duration? timeout,
  });

  /// Create a streaming connection with cancellation support
  StreamingConnection createCancellableStream({
    required String path,
    Map<String, dynamic>? queryParameters,
    Map<String, dynamic>? body,
    Map<String, String>? headers,
    Duration? timeout,
  });

  /// Cancel all active streams
  Future<void> cancelAllStreams();

  /// Get the number of active streams
  int get activeStreamCount;
}

/// Streaming connection wrapper with cancellation support
class StreamingConnection {
  final Stream<StreamingResponse<StreamingChunk>> stream;
  final CancelToken cancelToken;
  final VoidCallback onCancel;

  StreamingConnection({
    required this.stream,
    required this.cancelToken,
    required this.onCancel,
  });

  /// Cancel this specific stream
  void cancel() {
    cancelToken.cancel('Stream cancelled by user');
    onCancel();
  }

  /// Check if the stream is cancelled
  bool get isCancelled => cancelToken.isCancelled;
}

/// Implementation of streaming repository
class StreamingRepositoryImpl implements StreamingRepository {
  final StreamingServiceImpl _streamingService;
  final Set<CancelToken> _activeCancelTokens = <CancelToken>{};

  StreamingRepositoryImpl({
    required StreamingServiceImpl streamingService,
  }) : _streamingService = streamingService;

  @override
  Stream<StreamingResponse<StreamingChunk>> createStream({
    required String path,
    Map<String, dynamic>? queryParameters,
    Map<String, dynamic>? body,
    Map<String, String>? headers,
    Duration? timeout,
  }) {
    final cancelToken = CancelToken();
    _activeCancelTokens.add(cancelToken);

    return _streamingService
        .createSSEStream(
          path: path,
          queryParameters: queryParameters,
          body: body,
          headers: headers,
          timeout: timeout,
          cancelToken: cancelToken,
        )
        .handleError((error) {
          _activeCancelTokens.remove(cancelToken);
          throw _convertToStreamingException(error);
        })
        .doOnDone(() {
          _activeCancelTokens.remove(cancelToken);
        });
  }

  @override
  StreamingConnection createCancellableStream({
    required String path,
    Map<String, dynamic>? queryParameters,
    Map<String, dynamic>? body,
    Map<String, String>? headers,
    Duration? timeout,
  }) {
    final cancelToken = CancelToken();
    _activeCancelTokens.add(cancelToken);

    final stream = _streamingService
        .createSSEStream(
          path: path,
          queryParameters: queryParameters,
          body: body,
          headers: headers,
          timeout: timeout,
          cancelToken: cancelToken,
        )
        .handleError((error) {
          _activeCancelTokens.remove(cancelToken);
          throw _convertToStreamingException(error);
        })
        .doOnDone(() {
          _activeCancelTokens.remove(cancelToken);
        });

    return StreamingConnection(
      stream: stream,
      cancelToken: cancelToken,
      onCancel: () => _activeCancelTokens.remove(cancelToken),
    );
  }

  @override
  Future<void> cancelAllStreams() async {
    final tokens = List<CancelToken>.from(_activeCancelTokens);
    _activeCancelTokens.clear();

    for (final token in tokens) {
      if (!token.isCancelled) {
        token.cancel('All streams cancelled');
      }
    }
  }

  @override
  int get activeStreamCount => _activeCancelTokens.length;

  /// Convert generic errors to streaming-specific exceptions
  StreamingException _convertToStreamingException(dynamic error) {
    if (error is StreamingException) {
      return error;
    }

    if (error is DioException) {
      switch (error.type) {
        case DioExceptionType.connectionTimeout:
        case DioExceptionType.receiveTimeout:
        case DioExceptionType.sendTimeout:
          return StreamTimeoutException(
            'Request timeout: ${error.message}',
            const Duration(seconds: 60),
            error,
          );
        case DioExceptionType.cancel:
          return StreamCancelledException(
            'Stream was cancelled',
            error,
          );
        case DioExceptionType.connectionError:
          return StreamConnectionException(
            'Connection error: ${error.message}',
            error,
          );
        case DioExceptionType.badResponse:
          final statusCode = error.response?.statusCode;
          if (statusCode == 429) {
            final retryAfter = _extractRetryAfter(error.response?.headers);
            return StreamRateLimitException(
              'Rate limit exceeded',
              retryAfter,
              error,
            );
          }
          return StreamConnectionException(
            'HTTP error $statusCode: ${error.message}',
            error,
          );
        default:
          return StreamConnectionException(
            'Network error: ${error.message}',
            error,
          );
      }
    }

    if (error is FormatException) {
      return StreamParsingException(
        'Failed to parse streaming response: ${error.message}',
        error,
      );
    }

    if (error is TimeoutException) {
      return StreamTimeoutException(
        'Stream timeout: ${error.message}',
        error.duration ?? const Duration(seconds: 60),
        error,
      );
    }

    return StreamConnectionException(
      'Unexpected error: ${error.toString()}',
      error,
    );
  }

  /// Extract retry-after header value
  int? _extractRetryAfter(Headers? headers) {
    if (headers == null) return null;

    final retryAfterHeader = headers.value('retry-after');
    if (retryAfterHeader != null) {
      return int.tryParse(retryAfterHeader);
    }

    return null;
  }
}

/// Extension to add doOnDone functionality to streams
extension StreamExtensions<T> on Stream<T> {
  Stream<T> doOnDone(VoidCallback onDone) {
    return transform(StreamTransformer<T, T>.fromHandlers(
      handleDone: (sink) {
        onDone();
        sink.close();
      },
    ));
  }
}
