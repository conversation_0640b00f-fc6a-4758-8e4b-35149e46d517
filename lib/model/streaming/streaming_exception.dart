/// Custom exceptions for streaming operations
abstract class StreamingException implements Exception {
  final String message;
  final dynamic originalError;

  const StreamingException(this.message, [this.originalError]);

  @override
  String toString() => 'StreamingException: $message';
}

/// Exception thrown when stream connection fails
class StreamConnectionException extends StreamingException {
  const StreamConnectionException(super.message, [super.originalError]);

  @override
  String toString() => 'StreamConnectionException: $message';
}

/// Exception thrown when stream is interrupted
class StreamInterruptedException extends StreamingException {
  const StreamInterruptedException(super.message, [super.originalError]);

  @override
  String toString() => 'StreamInterruptedException: $message';
}

/// Exception thrown when stream parsing fails
class StreamParsingException extends StreamingException {
  final String? rawData;

  const StreamParsingException(super.message, [super.originalError, this.rawData]);

  @override
  String toString() => 'StreamParsingException: $message${rawData != null ? ' (Raw data: $rawData)' : ''}';
}

/// Exception thrown when stream times out
class StreamTimeoutException extends StreamingException {
  final Duration timeout;

  const StreamTimeoutException(super.message, this.timeout, [super.originalError]);

  @override
  String toString() => 'StreamTimeoutException: $message (Timeout: ${timeout.inSeconds}s)';
}

/// Exception thrown when stream is cancelled by user
class StreamCancelledException extends StreamingException {
  const StreamCancelledException(super.message, [super.originalError]);

  @override
  String toString() => 'StreamCancelledException: $message';
}

/// Exception thrown when stream rate limit is exceeded
class StreamRateLimitException extends StreamingException {
  final int? retryAfter;

  const StreamRateLimitException(super.message, [this.retryAfter, super.originalError]);

  @override
  String toString() => 'StreamRateLimitException: $message${retryAfter != null ? ' (Retry after: ${retryAfter}s)' : ''}';
}
