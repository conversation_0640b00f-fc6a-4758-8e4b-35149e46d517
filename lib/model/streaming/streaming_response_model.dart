import 'package:json_annotation/json_annotation.dart';

part 'streaming_response_model.g.dart';

/// Base class for streaming response chunks
@JsonSerializable()
class StreamingChunk {
  final String? id;
  final String? object;
  final int? created;
  final String? model;
  final List<StreamingChoice>? choices;
  final StreamingUsage? usage;
  @JsonKey(name: 'finish_reason')
  final String? finishReason;

  const StreamingChunk({
    this.id,
    this.object,
    this.created,
    this.model,
    this.choices,
    this.usage,
    this.finishReason,
  });

  factory StreamingChunk.fromJson(Map<String, dynamic> json) =>
      _$StreamingChunkFromJson(json);

  Map<String, dynamic> toJson() => _$StreamingChunkToJson(this);
}

@JsonSerializable()
class StreamingChoice {
  final int? index;
  final StreamingDelta? delta;
  @JsonKey(name: 'finish_reason')
  final String? finishReason;

  const StreamingChoice({
    this.index,
    this.delta,
    this.finishReason,
  });

  factory StreamingChoice.fromJson(Map<String, dynamic> json) =>
      _$StreamingChoiceFromJson(json);

  Map<String, dynamic> toJson() => _$StreamingChoiceToJson(this);
}

@JsonSerializable()
class StreamingDelta {
  final String? role;
  final String? content;

  const StreamingDelta({
    this.role,
    this.content,
  });

  factory StreamingDelta.fromJson(Map<String, dynamic> json) =>
      _$StreamingDeltaFromJson(json);

  Map<String, dynamic> toJson() => _$StreamingDeltaToJson(this);
}

@JsonSerializable()
class StreamingUsage {
  @JsonKey(name: 'prompt_tokens')
  final int? promptTokens;
  @JsonKey(name: 'completion_tokens')
  final int? completionTokens;
  @JsonKey(name: 'total_tokens')
  final int? totalTokens;

  const StreamingUsage({
    this.promptTokens,
    this.completionTokens,
    this.totalTokens,
  });

  factory StreamingUsage.fromJson(Map<String, dynamic> json) =>
      _$StreamingUsageFromJson(json);

  Map<String, dynamic> toJson() => _$StreamingUsageToJson(this);
}

/// Server-Sent Event data model
class SSEData {
  final String? id;
  final String? event;
  final String? data;
  final int? retry;

  const SSEData({
    this.id,
    this.event,
    this.data,
    this.retry,
  });

  factory SSEData.fromRawData(String rawData) {
    String? id;
    String? event;
    String? data;
    int? retry;

    final lines = rawData.split('\n');
    for (final line in lines) {
      if (line.startsWith('id:')) {
        id = line.substring(3).trim();
      } else if (line.startsWith('event:')) {
        event = line.substring(6).trim();
      } else if (line.startsWith('data:')) {
        data = line.substring(5).trim();
      } else if (line.startsWith('retry:')) {
        retry = int.tryParse(line.substring(6).trim());
      }
    }

    return SSEData(
      id: id,
      event: event,
      data: data,
      retry: retry,
    );
  }

  bool get isEmpty => data == null || data!.isEmpty;
  bool get isEndOfStream => data == '[DONE]';
}

/// Streaming state management
enum StreamingState {
  idle,
  connecting,
  streaming,
  completed,
  error,
  cancelled,
}

/// Streaming response wrapper
class StreamingResponse<T> {
  final StreamingState state;
  final T? data;
  final String? error;
  final double? progress;

  const StreamingResponse({
    required this.state,
    this.data,
    this.error,
    this.progress,
  });

  factory StreamingResponse.idle() => const StreamingResponse(
        state: StreamingState.idle,
      );

  factory StreamingResponse.connecting() => const StreamingResponse(
        state: StreamingState.connecting,
      );

  factory StreamingResponse.streaming(T data, {double? progress}) =>
      StreamingResponse(
        state: StreamingState.streaming,
        data: data,
        progress: progress,
      );

  factory StreamingResponse.completed(T data) => StreamingResponse(
        state: StreamingState.completed,
        data: data,
      );

  factory StreamingResponse.error(String error) => StreamingResponse(
        state: StreamingState.error,
        error: error,
      );

  factory StreamingResponse.cancelled() => const StreamingResponse(
        state: StreamingState.cancelled,
      );

  bool get isIdle => state == StreamingState.idle;
  bool get isConnecting => state == StreamingState.connecting;
  bool get isStreaming => state == StreamingState.streaming;
  bool get isCompleted => state == StreamingState.completed;
  bool get isError => state == StreamingState.error;
  bool get isCancelled => state == StreamingState.cancelled;
  bool get hasData => data != null;
}
