// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'follower_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

FollowerListModel _$FollowerListModelFromJson(Map<String, dynamic> json) =>
    FollowerListModel(
      followers: (json['followers'] as List<dynamic>?)
              ?.map((e) => UserModel.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      total: (json['total'] as num?)?.toInt() ?? 0,
    );

Map<String, dynamic> _$FollowerListModelToJson(FollowerListModel instance) =>
    <String, dynamic>{
      'followers': instance.followers,
      'total': instance.total,
    };

FollowingListModel _$FollowingListModelFromJson(Map<String, dynamic> json) =>
    FollowingListModel(
      following: (json['following'] as List<dynamic>?)
              ?.map((e) => UserModel.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      total: (json['total'] as num?)?.toInt() ?? 0,
    );

Map<String, dynamic> _$FollowingListModelToJson(FollowingListModel instance) =>
    <String, dynamic>{
      'following': instance.following,
      'total': instance.total,
    };

FollowerRelationshipModel _$FollowerRelationshipModelFromJson(
        Map<String, dynamic> json) =>
    FollowerRelationshipModel(
      id: json['id'] as String,
      followerId: json['follower_id'] as String,
      followedId: json['followed_id'] as String,
      status: json['status'] as String,
      followedAt: json['followed_at'] as String,
      metadata: json['metadata'] as Map<String, dynamic>?,
      createdAt: json['created_at'] as String,
      updatedAt: json['updated_at'] as String,
      follower: UserModel.fromJson(json['follower'] as Map<String, dynamic>),
      followed: UserModel.fromJson(json['followed'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$FollowerRelationshipModelToJson(
        FollowerRelationshipModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'follower_id': instance.followerId,
      'followed_id': instance.followedId,
      'status': instance.status,
      'followed_at': instance.followedAt,
      'metadata': instance.metadata,
      'created_at': instance.createdAt,
      'updated_at': instance.updatedAt,
      'follower': instance.follower,
      'followed': instance.followed,
    };

FollowerResponseDataModel _$FollowerResponseDataModelFromJson(
        Map<String, dynamic> json) =>
    FollowerResponseDataModel(
      items: (json['items'] as List<dynamic>?)
              ?.map((e) =>
                  FollowerRelationshipModel.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      nextCursor: json['next_cursor'] as String?,
      hasMore: json['has_more'] as bool? ?? false,
    );

Map<String, dynamic> _$FollowerResponseDataModelToJson(
        FollowerResponseDataModel instance) =>
    <String, dynamic>{
      'items': instance.items,
      'next_cursor': instance.nextCursor,
      'has_more': instance.hasMore,
    };

FollowerApiResponseModel _$FollowerApiResponseModelFromJson(
        Map<String, dynamic> json) =>
    FollowerApiResponseModel(
      unixTime: (json['unix_time'] as num).toInt(),
      data: FollowerResponseDataModel.fromJson(
          json['data'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$FollowerApiResponseModelToJson(
        FollowerApiResponseModel instance) =>
    <String, dynamic>{
      'unix_time': instance.unixTime,
      'data': instance.data,
    };
