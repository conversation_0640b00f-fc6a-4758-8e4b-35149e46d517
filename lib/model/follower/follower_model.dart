import 'package:json_annotation/json_annotation.dart';
import 'package:toii_social/model/user/user_model.dart';

part 'follower_model.g.dart';

// Original models for backward compatibility
@JsonSerializable()
class FollowerListModel {
  const FollowerListModel({this.followers = const [], required this.total});

  final List<UserModel> followers;
  @JsonKey(defaultValue: 0)
  final int total;

  factory FollowerListModel.fromJson(Map<String, dynamic> json) =>
      _$FollowerListModelFromJson(json);

  Map<String, dynamic> toJson() => _$FollowerListModelToJson(this);
}

@JsonSerializable()
class FollowingListModel {
  const FollowingListModel({this.following = const [], required this.total});

  final List<UserModel> following;
  @JsonKey(defaultValue: 0)
  final int total;

  factory FollowingListModel.fromJson(Map<String, dynamic> json) =>
      _$FollowingListModelFromJson(json);

  Map<String, dynamic> toJson() => _$FollowingListModelToJson(this);
}

// New models for API response structure
@JsonSerializable()
class FollowerRelationshipModel {
  const FollowerRelationshipModel({
    required this.id,
    required this.followerId,
    required this.followedId,
    required this.status,
    required this.followedAt,
    this.metadata,
    required this.createdAt,
    required this.updatedAt,
    required this.follower,
    required this.followed,
  });

  final String id;
  @JsonKey(name: 'follower_id')
  final String followerId;
  @JsonKey(name: 'followed_id')
  final String followedId;
  final String status;
  @JsonKey(name: 'followed_at')
  final String followedAt;
  final Map<String, dynamic>? metadata;
  @JsonKey(name: 'created_at')
  final String createdAt;
  @JsonKey(name: 'updated_at')
  final String updatedAt;
  final UserModel follower;
  final UserModel followed;

  factory FollowerRelationshipModel.fromJson(Map<String, dynamic> json) =>
      _$FollowerRelationshipModelFromJson(json);

  Map<String, dynamic> toJson() => _$FollowerRelationshipModelToJson(this);
}

@JsonSerializable()
class FollowerResponseDataModel {
  const FollowerResponseDataModel({
    this.items = const [],
    this.nextCursor,
    this.hasMore = false,
  });

  final List<FollowerRelationshipModel> items;
  @JsonKey(name: 'next_cursor')
  final String? nextCursor;
  @JsonKey(name: 'has_more')
  final bool hasMore;

  factory FollowerResponseDataModel.fromJson(Map<String, dynamic> json) =>
      _$FollowerResponseDataModelFromJson(json);

  Map<String, dynamic> toJson() => _$FollowerResponseDataModelToJson(this);

  // Helper method to convert to FollowerListModel
  FollowerListModel toFollowerListModel() {
    final followers = items.map((item) => item.follower).toList();
    return FollowerListModel(
      followers: followers,
      total:
          followers.length, // Since API doesn't provide total, use items length
    );
  }

  // Helper method to convert to FollowingListModel
  FollowingListModel toFollowingListModel() {
    final following = items.map((item) => item.followed).toList();
    return FollowingListModel(
      following: following,
      total:
          following.length, // Since API doesn't provide total, use items length
    );
  }
}

@JsonSerializable()
class FollowerApiResponseModel {
  const FollowerApiResponseModel({required this.unixTime, required this.data});

  @JsonKey(name: 'unix_time')
  final int unixTime;
  final FollowerResponseDataModel data;

  factory FollowerApiResponseModel.fromJson(Map<String, dynamic> json) =>
      _$FollowerApiResponseModelFromJson(json);

  Map<String, dynamic> toJson() => _$FollowerApiResponseModelToJson(this);
}
